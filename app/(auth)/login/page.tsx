"use client"

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { authClient } from "@/lib/auth-client";
import { Provider } from "@radix-ui/react-tooltip";
import { GithubIcon, Loader } from "lucide-react";
import { useTransition } from "react";
import { toast } from "sonner";
import { LoginForm } from "./_components/LoginForm";

export default function LoginPage(){
    const [githubPending, startGithubTransition] = useTransition()


    async function signInWithGithub(){
        startGithubTransition(async () => {
            await authClient.signIn.social({
                provider: 'github',
                callbackURL: "/",
                fetchOptions: {
                    onSuccess: () => {
                        toast.success('Signed in with <PERSON><PERSON><PERSON>, you will be redirected...')
                    },
                    onError: (error) => {
                        toast.error("Internal sever error!")
                    }
                },
            });
        })
    }
    return(
        <LoginForm/>
    )
}