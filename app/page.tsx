"use client";

import { But<PERSON> } from "@/components/ui/button";
import { ThemeToggle } from "@/components/ui/themeToggle";
import { authClient } from "@/lib/auth-client";

export default function Home() {
  const { data: session } = authClient.useSession();
  async function signOut() {
    await authClient.signOut({
      fetchOptions: {
        onSuccess: () => {
          router.push("/login"); // redirect to login page
        },
        
      },
    });
  }

  return (
    <div className="p-24">
      <h1>Hello world!</h1>
      <ThemeToggle />

      {session ? (
        <div>
          <p>{session.user.name} </p>
          <Button>Logout</Button>
        </div>
      ) : (
        <Button>Log out</Button>
      )}
    </div>
  );
}
